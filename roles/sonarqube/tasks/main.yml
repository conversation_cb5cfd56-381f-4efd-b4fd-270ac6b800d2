---
# SonarQube Community Edition installation

- name: Run pre-installation checks
  include_tasks: pre-checks.yml

# Clean SonarQube installation using system Docker
- name: Prepare system for SonarQube
  become: true
  block:
    - name: Set vm.max_map_count for Elasticsearch  
      ansible.posix.sysctl:
        name: vm.max_map_count
        value: '262144'
        state: present
        reload: true

    - name: Set fs.file-max for SonarQube
      ansible.posix.sysctl:
        name: fs.file-max
        value: '65536'
        state: present
        reload: true

    - name: Create SonarQube data directories
      ansible.builtin.file:
        path: "{{ item }}"
        state: directory
        owner: "999"  # SonarQube container user
        group: "999"  # SonarQube container group  
        mode: '0755'
      loop:
        - "{{ sonar_data_dir }}"
        - "{{ sonar_data_dir }}/data"
        - "{{ sonar_data_dir }}/logs"
        - "{{ sonar_data_dir }}/extensions"
        - "{{ sonar_data_dir }}/compose"

    - name: Create Nginx directories
      ansible.builtin.file:
        path: "{{ item }}"
        state: directory
        owner: "{{ ansible_user }}"
        mode: '0755'
      loop:
        - "{{ sonar_data_dir }}/nginx"
        - "{{ sonar_data_dir }}/nginx/certs"
      when: nginx_enabled

- name: Deploy SonarQube with Docker Compose
  become: false
  block:
    - name: Ensure Docker context is default (system Docker)
      ansible.builtin.command: docker context use default
      changed_when: false
      ignore_errors: true

    - name: Stop any existing SonarQube containers
      community.docker.docker_container:
        name: sonarqube
        state: absent
      ignore_errors: true

    - name: Clean up any corrupted Elasticsearch data
      ansible.builtin.file:
        path: "{{ sonar_data_dir }}/data/es8"
        state: absent
      become: true
      ignore_errors: true

    - name: Template docker-compose.yml
      ansible.builtin.template:
        src: docker-compose.yml.j2
        dest: "{{ sonar_data_dir }}/compose/docker-compose.yml"
        owner: "{{ ansible_user }}"
        mode: '0644'
      become: true

    - name: Template nginx configuration
      ansible.builtin.template:
        src: nginx.conf.j2
        dest: "{{ sonar_data_dir }}/nginx/nginx.conf"
        owner: "{{ ansible_user }}"
        mode: '0644'
      become: true
      when: nginx_enabled

    - name: Generate self-signed SSL certificate for nginx
      block:
        - name: Check if SSL certificate exists
          ansible.builtin.stat:
            path: "{{ sonar_data_dir }}/nginx/certs/server.crt"
          register: ssl_cert

        - name: Generate SSL certificate
          ansible.builtin.shell: |
            openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout {{ sonar_data_dir }}/nginx/certs/server.key \
            -out {{ sonar_data_dir }}/nginx/certs/server.crt \
            -subj "/C=US/ST=State/L=City/O=Organization/OU=OrgUnit/CN={{ nginx_server_name }}"
          when: not ssl_cert.stat.exists
          become: true

        - name: Set SSL certificate permissions
          ansible.builtin.file:
            path: "{{ item }}"
            owner: "{{ ansible_user }}"
            mode: '0600'
          loop:
            - "{{ sonar_data_dir }}/nginx/certs/server.crt"
            - "{{ sonar_data_dir }}/nginx/certs/server.key"
          become: true
          when: not ssl_cert.stat.exists
      when: nginx_enabled

    - name: Set correct permissions on SSL certificates (if they exist)
      ansible.builtin.file:
        path: "{{ item }}"
        owner: "root"
        group: "root"
        mode: '0600'
      loop:
        - "{{ sonar_data_dir }}/nginx/certs/server.crt"
        - "{{ sonar_data_dir }}/nginx/certs/server.key"
      become: true
      when: nginx_enabled
      ignore_errors: true

    - name: Detect container user ID
      ansible.builtin.command: docker run --rm --entrypoint="" {{ sonar_image }}:{{ sonar_version }} id -u
      register: container_uid
      changed_when: false

    - name: Fix directory ownership for container user  
      ansible.builtin.file:
        path: "{{ item }}"
        owner: "{{ container_uid.stdout }}"
        group: "{{ container_uid.stdout }}"
        recurse: true
      loop:
        - "{{ sonar_data_dir }}/data"
        - "{{ sonar_data_dir }}/logs"
        - "{{ sonar_data_dir }}/extensions"
      become: true

    - name: Display container user info
      ansible.builtin.debug:
        msg: "🔧 Fixed permissions for container user ID: {{ container_uid.stdout }}"

    - name: Start SonarQube with Docker Compose
      community.docker.docker_compose_v2:
        project_src: "{{ sonar_data_dir }}/compose"
        state: present

    - name: Wait for SonarQube to be ready
      ansible.builtin.uri:
        url: "{{ 'https://localhost:' + nginx_ssl_port|string if nginx_enabled else 'http://localhost:' + sonar_port|string }}/api/system/status"
        method: GET
        status_code: 200
        validate_certs: false
      register: sonar_status
      until: sonar_status.status == 200
      retries: 30
      delay: 10

    - name: Display SonarQube access information
      ansible.builtin.debug:
        msg:
          - "SonarQube Community Edition is now running!"
          - "Access URL: http://{{ ansible_host }}:{{ sonar_port }}"
          - "Default login: admin/admin"

- name: Run post-installation checks
  include_tasks: post-checks.yml
