---
# SonarQube Application Settings
sonar_version: "25.8.0.112029-community"
sonar_image: "docker-all.dach041.dachser.com/sonarqube"
sonar_port: 9000
sonar_data_dir: "/opt/sonarqube"

# Nginx Reverse Proxy Settings
nginx_enabled: true
nginx_port: 80
nginx_ssl_port: 443
nginx_server_name: "{{ ansible_host }}"
nginx_image: "docker-all.dach041.dachser.com/nginx"
nginx_version: "1.29.1-alpine-slim"

# Database Settings (configured for production with existing MSSQL)
sonar_db_type: "sqlserver"
sonar_db_host: "SQL-PROD-SONARQUBE.dachser-ds.dachser.com"
sonar_db_port: "1433"
sonar_db_name: "Sonarqube"
sonar_db_user: "Sonarqube"
sonar_db_password: "SonarQu100%"
sonar_db_instance: "" # Optional: SQL Server instance name

# Database SSL Settings
sonar_db_encrypt: "false"  # Set to "true" to enable SSL encryption
sonar_db_trust_server_certificate: "true"  # Set to "true" to bypass certificate validation
